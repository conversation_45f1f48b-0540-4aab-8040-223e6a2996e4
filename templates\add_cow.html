{% extends "base.html" %}

{% block title %}إضافة بقرة جديدة - نظام إدارة مزرعة الألبان{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-plus"></i> إضافة بقرة جديدة
            </h1>
            <a href="{{ url_for('cows_list') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> العودة للقائمة
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-cow"></i> بيانات البقرة
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.name.label(class="form-label") }}
                                {{ form.name(class="form-control") }}
                                {% if form.name.errors %}
                                    <div class="text-danger">
                                        {% for error in form.name.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.tag_number.label(class="form-label") }}
                                {{ form.tag_number(class="form-control") }}
                                {% if form.tag_number.errors %}
                                    <div class="text-danger">
                                        {% for error in form.tag_number.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.breed.label(class="form-label") }}
                                {{ form.breed(class="form-select") }}
                                {% if form.breed.errors %}
                                    <div class="text-danger">
                                        {% for error in form.breed.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.birth_date.label(class="form-label") }}
                                {{ form.birth_date(class="form-control") }}
                                {% if form.birth_date.errors %}
                                    <div class="text-danger">
                                        {% for error in form.birth_date.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.weight.label(class="form-label") }}
                                <div class="input-group">
                                    {{ form.weight(class="form-control") }}
                                    <span class="input-group-text">كيلو</span>
                                </div>
                                {% if form.weight.errors %}
                                    <div class="text-danger">
                                        {% for error in form.weight.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.health_status.label(class="form-label") }}
                                {{ form.health_status(class="form-select") }}
                                {% if form.health_status.errors %}
                                    <div class="text-danger">
                                        {% for error in form.health_status.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <div class="form-check">
                                    {{ form.pregnancy_status(class="form-check-input") }}
                                    {{ form.pregnancy_status.label(class="form-check-label") }}
                                </div>
                                {% if form.pregnancy_status.errors %}
                                    <div class="text-danger">
                                        {% for error in form.pregnancy_status.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                {{ form.lactation_period.label(class="form-label") }}
                                <div class="input-group">
                                    {{ form.lactation_period(class="form-control") }}
                                    <span class="input-group-text">يوم</span>
                                </div>
                                {% if form.lactation_period.errors %}
                                    <div class="text-danger">
                                        {% for error in form.lactation_period.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                {{ form.daily_milk_production.label(class="form-label") }}
                                <div class="input-group">
                                    {{ form.daily_milk_production(class="form-control") }}
                                    <span class="input-group-text">لتر</span>
                                </div>
                                {% if form.daily_milk_production.errors %}
                                    <div class="text-danger">
                                        {% for error in form.daily_milk_production.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('cows_list') }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times"></i> إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ البقرة
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
