{% extends "base.html" %}

{% block title %}تفاصيل مكون العلف - {{ ingredient.name }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-seedling"></i> تفاصيل مكون العلف: {{ ingredient.name }}
            </h1>
            <div>
                <a href="{{ url_for('edit_feed_ingredient', ingredient_id=ingredient.id) }}" class="btn btn-warning">
                    <i class="fas fa-edit"></i> تعديل
                </a>
                <a href="{{ url_for('feed_ingredients_list') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> العودة للقائمة
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle"></i> معلومات المكون
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">اسم المكون</label>
                            <div class="h5">{{ ingredient.name }}</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">تاريخ الإضافة</label>
                            <div class="h6">{{ ingredient.created_at.strftime('%Y-%m-%d %H:%M') }}</div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">المورد</label>
                            <div>{{ ingredient.supplier or 'غير محدد' }}</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">سعر الكيلو</label>
                            <div class="h5 text-success">{{ ingredient.price_per_kg }} ريال</div>
                        </div>
                    </div>
                </div>
                
                {% if ingredient.notes %}
                <div class="mb-3">
                    <label class="form-label text-muted">ملاحظات</label>
                    <div class="p-3 bg-light rounded">{{ ingredient.notes }}</div>
                </div>
                {% endif %}
            </div>
        </div>
        
        {% if ingredient.mix_ingredients %}
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-blender"></i> الخلطات التي تحتوي على هذا المكون
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead class="table-light">
                            <tr>
                                <th>اسم الخلطة</th>
                                <th>النسبة في الخلطة</th>
                                <th>المساهمة في التكلفة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for mix_ingredient in ingredient.mix_ingredients %}
                            <tr>
                                <td><strong>{{ mix_ingredient.feed_mix.name }}</strong></td>
                                <td>{{ mix_ingredient.percentage }}%</td>
                                <td>{{ "%.2f"|format((mix_ingredient.percentage / 100) * ingredient.price_per_kg) }} ريال</td>
                                <td>
                                    <a href="{{ url_for('feed_mix_details', mix_id=mix_ingredient.feed_mix.id) }}" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i> عرض الخلطة
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar"></i> القيم الغذائية
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label">نسبة البروتين</label>
                    <div class="progress mb-2">
                        <div class="progress-bar bg-primary" style="width: {{ (ingredient.protein_percentage / 50) * 100 }}%"></div>
                    </div>
                    <div class="h5 text-primary">{{ ingredient.protein_percentage }}%</div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">الطاقة</label>
                    <div class="progress mb-2">
                        <div class="progress-bar bg-success" style="width: {{ (ingredient.energy_mcal_kg / 5) * 100 }}%"></div>
                    </div>
                    <div class="h5 text-success">{{ ingredient.energy_mcal_kg }} ميجا كالوري/كيلو</div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">نسبة الألياف</label>
                    <div class="progress mb-2">
                        <div class="progress-bar bg-info" style="width: {{ (ingredient.fiber_percentage / 50) * 100 }}%"></div>
                    </div>
                    <div class="h5 text-info">{{ ingredient.fiber_percentage }}%</div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">نسبة الرطوبة</label>
                    <div class="progress mb-2">
                        <div class="progress-bar bg-warning" style="width: {{ (ingredient.moisture_percentage / 30) * 100 }}%"></div>
                    </div>
                    <div class="h5 text-warning">{{ ingredient.moisture_percentage }}%</div>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calculator"></i> إحصائيات الاستخدام
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label">عدد الخلطات المستخدم فيها</label>
                    <div class="h4 text-primary">{{ ingredient.mix_ingredients|length }}</div>
                </div>
                
                {% if ingredient.mix_ingredients %}
                <div class="mb-3">
                    <label class="form-label">متوسط النسبة في الخلطات</label>
                    {% set avg_percentage = (ingredient.mix_ingredients|sum(attribute='percentage')) / (ingredient.mix_ingredients|length) %}
                    <div class="h5 text-info">{{ "%.1f"|format(avg_percentage) }}%</div>
                </div>
                {% endif %}
                
                <div class="mb-3">
                    <label class="form-label">تصنيف المكون</label>
                    <div>
                        {% if ingredient.protein_percentage > 30 %}
                            <span class="badge bg-primary">مصدر بروتين</span>
                        {% elif ingredient.energy_mcal_kg > 3 %}
                            <span class="badge bg-success">مصدر طاقة</span>
                        {% elif ingredient.fiber_percentage > 20 %}
                            <span class="badge bg-info">مصدر ألياف</span>
                        {% else %}
                            <span class="badge bg-secondary">مكون متوازن</span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
