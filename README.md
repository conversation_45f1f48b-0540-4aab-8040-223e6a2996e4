# نظام إدارة مزرعة الألبان 🐄🥛

نظام شامل لإدارة مزارع الألبان باللغة العربية، مطور باستخدام Flask و SQLAlchemy.

## المميزات الرئيسية

### 📊 لوحة التحكم
- عرض إحصائيات شاملة للمزرعة
- إجمالي الأبقار والأبقار المنتجة والحوامل
- إنتاج الحليب اليومي ومتوسط الإنتاج لكل بقرة
- قائمة بأفضل الأبقار إنتاجاً

### 🐄 إدارة الأبقار
- تسجيل بيانات الأبقار الكاملة (الاسم، رقم الأذن، السلالة، تاريخ الولادة)
- تتبع الحالة الصحية وحالة الحمل
- مراقبة فترة الإدرار وإنتاج الحليب اليومي
- حساب العمر تلقائياً

### 🌾 إدارة مكونات العلف
- قاعدة بيانات شاملة لمكونات العلف
- تسجيل القيم الغذائية (البروتين، الطاقة، الألياف، الرطوبة)
- تتبع الأسعار والموردين
- 17 مكون علف جاهز مع قيمها الحقيقية

### 🥣 إدارة خلطات العلف
- إنشاء خلطات علف مخصصة
- حساب القيم الغذائية تلقائياً
- حساب التكلفة الإجمالية للخلطة
- مقارنة القيم المحسوبة مع الأهداف المطلوبة
- 5 خلطات جاهزة للاستخدام

## البيانات المتوفرة

### مكونات العلف المضافة:
- **الحبوب**: الذرة الصفراء، الشعير، القمح
- **مصادر البروتين**: كسبة فول الصويا، كسبة بذرة القطن، كسبة عباد الشمس
- **النخالة**: نخالة القمح، نخالة الأرز
- **الأعلاف الخضراء**: دريس البرسيم، تبن القمح
- **المكملات**: حجر الجير، ملح الطعام، فوسفات ثنائي الكالسيوم، خليط الفيتامينات
- **مصادر الطاقة**: زيت النخيل، دبس السكر، خميرة البيرة

### خلطات العلف الجاهزة:
1. **خلطة الأبقار عالية الإنتاج** - للأبقار التي تنتج +25 لتر يومياً
2. **خلطة الأبقار متوسطة الإنتاج** - للأبقار التي تنتج 15-25 لتر يومياً
3. **خلطة الأبقار الجافة** - للأبقار غير المنتجة
4. **خلطة العجول النامية** - للعجول من 6-18 شهر
5. **خلطة اقتصادية** - منخفضة التكلفة

### الأبقار النموذجية:
- 21 بقرة بأسماء عربية
- سلالات متنوعة (هولشتاين، جيرسي، براون سويس، أيرشاير، محلية)
- 15 بقرة منتجة بإجمالي 299.9 لتر يومياً
- 7 أبقار حوامل

## متطلبات التشغيل

```
Flask==2.3.3
Flask-SQLAlchemy==3.0.5
Flask-WTF==1.1.1
WTForms==3.0.1
Werkzeug==2.3.7
```

## طريقة التشغيل

1. **تثبيت المتطلبات:**
```bash
pip install -r requirements.txt
```

2. **تشغيل التطبيق:**
```bash
python app.py
```

3. **فتح التطبيق في المتصفح:**
```
http://127.0.0.1:5000
```

## إضافة البيانات النموذجية

لإضافة البيانات النموذجية، شغل الملفات التالية:

```bash
# إضافة مكونات العلف
python add_sample_data.py

# إضافة خلطات العلف
python add_sample_mixes.py

# إضافة الأبقار النموذجية
python add_sample_cows.py
```

## هيكل المشروع

```
├── app.py                 # التطبيق الرئيسي
├── models.py              # نماذج قاعدة البيانات
├── forms.py               # نماذج الإدخال
├── requirements.txt       # المتطلبات
├── add_sample_data.py     # إضافة مكونات العلف
├── add_sample_mixes.py    # إضافة خلطات العلف
├── add_sample_cows.py     # إضافة الأبقار النموذجية
├── templates/             # قوالب HTML
│   ├── base.html
│   ├── dashboard.html
│   ├── cows_list.html
│   ├── add_cow.html
│   ├── feed_ingredients_list.html
│   ├── add_feed_ingredient.html
│   ├── feed_mixes_list.html
│   ├── add_feed_mix.html
│   ├── configure_feed_mix.html
│   └── feed_mix_details.html
└── dairy_farm.db          # قاعدة البيانات (تُنشأ تلقائياً)
```

## الميزات التقنية

- **واجهة عربية كاملة** مع دعم RTL
- **تصميم متجاوب** باستخدام Bootstrap 5
- **قاعدة بيانات SQLite** محلية
- **حساب تلقائي** للقيم الغذائية والتكلفة
- **فلاتر مخصصة** لحساب العمر
- **رسائل تأكيد** للعمليات
- **واجهة سهلة الاستخدام**

## المطور

تم تطوير هذا النظام باستخدام Augment Agent لإدارة مزارع الألبان بكفاءة عالية.

---

**ملاحظة**: هذا النظام مخصص للاستخدام التعليمي والتطويري. للاستخدام في الإنتاج، يُنصح بإضافة ميزات الأمان والنسخ الاحتياطي.
