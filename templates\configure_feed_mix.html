{% extends "base.html" %}

{% block title %}تكوين خلطة العلف - {{ mix.name }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-cogs"></i> تكوين خلطة العلف: {{ mix.name }}
            </h1>
            <a href="{{ url_for('feed_mixes_list') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> العودة للقائمة
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-percentage"></i> نسب المكونات
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>تنبيه:</strong> يجب أن يكون مجموع النسب = 100%
                    </div>
                    
                    {% if ingredients %}
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead class="table-light">
                                    <tr>
                                        <th>المكون</th>
                                        <th>البروتين (%)</th>
                                        <th>الطاقة</th>
                                        <th>الألياف (%)</th>
                                        <th>السعر/كيلو</th>
                                        <th>النسبة في الخلطة (%)</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for ingredient in ingredients %}
                                    <tr>
                                        <td><strong>{{ ingredient.name }}</strong></td>
                                        <td>{{ ingredient.protein_percentage }}%</td>
                                        <td>{{ ingredient.energy_mcal_kg }}</td>
                                        <td>{{ ingredient.fiber_percentage }}%</td>
                                        <td>{{ ingredient.price_per_kg }} ريال</td>
                                        <td>
                                            <div class="input-group">
                                                <input type="number" 
                                                       name="ingredient_{{ ingredient.id }}" 
                                                       class="form-control ingredient-percentage" 
                                                       min="0" 
                                                       max="100" 
                                                       step="0.1"
                                                       value="{{ current_config.get(ingredient.id, '') }}"
                                                       placeholder="0.0">
                                                <span class="input-group-text">%</span>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <div class="alert alert-info">
                                    <strong>المجموع الحالي:</strong> 
                                    <span id="total-percentage" class="fw-bold">0.0</span>%
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> حفظ التكوين
                                    </button>
                                </div>
                            </div>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-exclamation-circle fa-2x text-warning mb-3"></i>
                            <h5>لا توجد مكونات علف متاحة</h5>
                            <p class="text-muted">يجب إضافة مكونات علف أولاً قبل تكوين الخلطة</p>
                            <a href="{{ url_for('add_feed_ingredient') }}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> إضافة مكون علف
                            </a>
                        </div>
                    {% endif %}
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-target"></i> الأهداف المطلوبة
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label">البروتين المستهدف</label>
                    <div class="h5 text-primary">{{ mix.target_protein }}%</div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">الطاقة المستهدفة</label>
                    <div class="h5 text-success">{{ mix.target_energy }} ميجا كالوري/كيلو</div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">الألياف المستهدفة</label>
                    <div class="h5 text-info">{{ mix.target_fiber }}%</div>
                </div>
            </div>
        </div>
        
        {% if mix.calculated_protein > 0 %}
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calculator"></i> القيم المحسوبة
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label">البروتين الفعلي</label>
                    <div class="h5 text-primary">{{ mix.calculated_protein }}%</div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">الطاقة الفعلية</label>
                    <div class="h5 text-success">{{ mix.calculated_energy }} ميجا كالوري/كيلو</div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">الألياف الفعلية</label>
                    <div class="h5 text-info">{{ mix.calculated_fiber }}%</div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">التكلفة الإجمالية</label>
                    <div class="h5 text-warning">{{ mix.total_cost_per_kg }} ريال/كيلو</div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const percentageInputs = document.querySelectorAll('.ingredient-percentage');
    const totalElement = document.getElementById('total-percentage');
    
    function updateTotal() {
        let total = 0;
        percentageInputs.forEach(input => {
            const value = parseFloat(input.value) || 0;
            total += value;
        });
        totalElement.textContent = total.toFixed(1);
        
        // تغيير لون المجموع حسب القيمة
        if (Math.abs(total - 100) <= 0.1) {
            totalElement.className = 'fw-bold text-success';
        } else {
            totalElement.className = 'fw-bold text-danger';
        }
    }
    
    percentageInputs.forEach(input => {
        input.addEventListener('input', updateTotal);
    });
    
    // حساب المجموع الأولي
    updateTotal();
});
</script>
{% endblock %}
