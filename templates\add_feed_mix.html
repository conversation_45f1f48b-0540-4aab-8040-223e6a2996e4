{% extends "base.html" %}

{% block title %}إضافة خلطة علف - نظام إدارة مزرعة الألبان{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-plus"></i> إضافة خلطة علف جديدة
            </h1>
            <a href="{{ url_for('feed_mixes_list') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> العودة للقائمة
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-blender"></i> بيانات خلطة العلف
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.name.label(class="form-label") }}
                        {{ form.name(class="form-control") }}
                        {% if form.name.errors %}
                            <div class="text-danger">
                                {% for error in form.name.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.description.label(class="form-label") }}
                        {{ form.description(class="form-control", rows="3") }}
                        {% if form.description.errors %}
                            <div class="text-danger">
                                {% for error in form.description.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                {{ form.target_protein.label(class="form-label") }}
                                <div class="input-group">
                                    {{ form.target_protein(class="form-control") }}
                                    <span class="input-group-text">%</span>
                                </div>
                                {% if form.target_protein.errors %}
                                    <div class="text-danger">
                                        {% for error in form.target_protein.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                {{ form.target_energy.label(class="form-label") }}
                                <div class="input-group">
                                    {{ form.target_energy(class="form-control") }}
                                    <span class="input-group-text">ميجا كالوري/كيلو</span>
                                </div>
                                {% if form.target_energy.errors %}
                                    <div class="text-danger">
                                        {% for error in form.target_energy.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                {{ form.target_fiber.label(class="form-label") }}
                                <div class="input-group">
                                    {{ form.target_fiber(class="form-control") }}
                                    <span class="input-group-text">%</span>
                                </div>
                                {% if form.target_fiber.errors %}
                                    <div class="text-danger">
                                        {% for error in form.target_fiber.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>ملاحظة:</strong> بعد إنشاء الخلطة، ستتمكن من تكوين مكونات الخلطة ونسبها.
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('feed_mixes_list') }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times"></i> إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ الخلطة
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
