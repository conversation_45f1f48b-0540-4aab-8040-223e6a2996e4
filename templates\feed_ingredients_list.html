{% extends "base.html" %}

{% block title %}مكونات العلف - نظام إدارة مزرعة الألبان{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-seedling"></i> مكونات العلف
            </h1>
            <a href="{{ url_for('add_feed_ingredient') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> إضافة مكون جديد
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                {% if ingredients %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>اسم المكون</th>
                                    <th>البروتين (%)</th>
                                    <th>الطاقة (ميجا كالوري/كيلو)</th>
                                    <th>الألياف (%)</th>
                                    <th>الرطوبة (%)</th>
                                    <th>السعر (كيلو)</th>
                                    <th>المورد</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for ingredient in ingredients %}
                                <tr>
                                    <td><strong>{{ ingredient.name }}</strong></td>
                                    <td>{{ ingredient.protein_percentage }}%</td>
                                    <td>{{ ingredient.energy_mcal_kg }}</td>
                                    <td>{{ ingredient.fiber_percentage }}%</td>
                                    <td>{{ ingredient.moisture_percentage }}%</td>
                                    <td>{{ ingredient.price_per_kg }} ريال</td>
                                    <td>{{ ingredient.supplier or '-' }}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <button type="button" class="btn btn-outline-primary" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-warning" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-danger" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-seedling fa-3x text-muted mb-3"></i>
                        <h4 class="text-muted">لا توجد مكونات علف مسجلة</h4>
                        <p class="text-muted">ابدأ بإضافة أول مكون علف</p>
                        <a href="{{ url_for('add_feed_ingredient') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> إضافة مكون جديد
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
