# models.py
from flask_sqlalchemy import SQLAlchemy
from datetime import datetime, date

db = SQLAlchemy()

class Cow(db.Model):
    """نموذج البقرة"""
    __tablename__ = 'cows'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    tag_number = db.Column(db.String(50), unique=True, nullable=False)
    breed = db.Column(db.String(50), nullable=False)
    birth_date = db.Column(db.Date, nullable=False)
    weight = db.Column(db.Float, nullable=False)
    health_status = db.Column(db.String(50), default='سليمة')
    pregnancy_status = db.Column(db.<PERSON>, default=False)
    lactation_period = db.Column(db.Integer, default=0)  # بالأيام
    daily_milk_production = db.Column(db.Float, default=0.0)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # العلاقات
    milk_records = db.relationship('MilkRecord', backref='cow', lazy=True, cascade='all, delete-orphan')
    feeding_records = db.relationship('FeedingRecord', backref='cow', lazy=True, cascade='all, delete-orphan')

    def __repr__(self):
        return f'<Cow {self.name} - {self.tag_number}>'

class FeedIngredient(db.Model):
    """مكونات العلف"""
    __tablename__ = 'feed_ingredients'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    protein_percentage = db.Column(db.Float, nullable=False)  # نسبة البروتين %
    energy_mcal_kg = db.Column(db.Float, nullable=False)      # الطاقة ميجا كالوري/كيلو
    fiber_percentage = db.Column(db.Float, nullable=False)    # نسبة الألياف %
    moisture_percentage = db.Column(db.Float, default=10.0)   # نسبة الرطوبة %
    price_per_kg = db.Column(db.Float, nullable=False)        # سعر الكيلو
    supplier = db.Column(db.String(100))
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f'<FeedIngredient {self.name}>'

class FeedMix(db.Model):
    """خلطات العلف"""
    __tablename__ = 'feed_mixes'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    description = db.Column(db.Text)
    target_protein = db.Column(db.Float, nullable=False)      # البروتين المستهدف %
    target_energy = db.Column(db.Float, nullable=False)       # الطاقة المستهدفة
    target_fiber = db.Column(db.Float, nullable=False)        # الألياف المستهدفة %
    calculated_protein = db.Column(db.Float, default=0.0)     # البروتين المحسوب فعلياً
    calculated_energy = db.Column(db.Float, default=0.0)      # الطاقة المحسوبة فعلياً
    calculated_fiber = db.Column(db.Float, default=0.0)       # الألياف المحسوبة فعلياً
    total_cost_per_kg = db.Column(db.Float, default=0.0)      # التكلفة الإجمالية
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # العلاقات
    ingredients = db.relationship('FeedMixIngredient', backref='feed_mix', lazy=True, cascade='all, delete-orphan')
    feeding_records = db.relationship('FeedingRecord', backref='feed_mix', lazy=True)

    def calculate_nutritional_values(self):
        """حساب القيم الغذائية للخلطة"""
        total_protein = 0.0
        total_energy = 0.0
        total_fiber = 0.0
        total_cost = 0.0
        total_weight = sum(ingredient.percentage for ingredient in self.ingredients)
        
        if total_weight > 0:
            for mix_ingredient in self.ingredients:
                ingredient = mix_ingredient.ingredient
                weight_ratio = mix_ingredient.percentage / 100.0
                
                total_protein += weight_ratio * ingredient.protein_percentage
                total_energy += weight_ratio * ingredient.energy_mcal_kg
                total_fiber += weight_ratio * ingredient.fiber_percentage
                total_cost += weight_ratio * ingredient.price_per_kg
            
            self.calculated_protein = round(total_protein, 2)
            self.calculated_energy = round(total_energy, 2)
            self.calculated_fiber = round(total_fiber, 2)
            self.total_cost_per_kg = round(total_cost, 2)

    def __repr__(self):
        return f'<FeedMix {self.name}>'

class FeedMixIngredient(db.Model):
    """مكونات خلطة العلف"""
    __tablename__ = 'feed_mix_ingredients'
    
    id = db.Column(db.Integer, primary_key=True)
    feed_mix_id = db.Column(db.Integer, db.ForeignKey('feed_mixes.id'), nullable=False)
    ingredient_id = db.Column(db.Integer, db.ForeignKey('feed_ingredients.id'), nullable=False)
    percentage = db.Column(db.Float, nullable=False)  # النسبة المئوية في الخلطة
    
    ingredient = db.relationship('FeedIngredient', backref='mix_ingredients')

class MilkRecord(db.Model):
    """سجل إنتاج الحليب"""
    __tablename__ = 'milk_records'
    
    id = db.Column(db.Integer, primary_key=True)
    cow_id = db.Column(db.Integer, db.ForeignKey('cows.id'), nullable=False)
    date = db.Column(db.Date, nullable=False)
    morning_quantity = db.Column(db.Float, default=0.0)
    evening_quantity = db.Column(db.Float, default=0.0)
    total_quantity = db.Column(db.Float, nullable=False)
    fat_percentage = db.Column(db.Float, default=3.5)
    protein_percentage = db.Column(db.Float, default=3.2)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class FeedingRecord(db.Model):
    """سجل التغذية"""
    __tablename__ = 'feeding_records'
    
    id = db.Column(db.Integer, primary_key=True)
    cow_id = db.Column(db.Integer, db.ForeignKey('cows.id'), nullable=False)
    feed_mix_id = db.Column(db.Integer, db.ForeignKey('feed_mixes.id'), nullable=False)
    date = db.Column(db.Date, nullable=False)
    quantity_kg = db.Column(db.Float, nullable=False)
    feeding_time = db.Column(db.String(20), nullable=False)  # صباح، ظهر، مساء
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
