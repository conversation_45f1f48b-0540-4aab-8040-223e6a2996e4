# app.py
from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
from flask_sqlalchemy import SQLAlchemy
from datetime import datetime, date
import os

app = Flask(__name__)

# إعدادات التطبيق
app.config['SECRET_KEY'] = 'your-secret-key-change-in-production'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///dairy_farm.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# استيراد النماذج والاستمارات
from models import db, Cow, FeedIngredient, FeedMix, FeedMixIngredient, MilkRecord, FeedingRecord
from forms import CowForm, FeedIngredientForm, FeedMixForm

db.init_app(app)

# دالة مساعدة لحساب العمر
@app.template_filter('age')
def calculate_age(birth_date):
    """حساب العمر بالأيام والسنوات والشهور"""
    if not birth_date:
        return '-'

    today = date.today()
    age_days = (today - birth_date).days

    if age_days < 30:
        return f"{age_days} يوم"
    elif age_days < 365:
        months = round(age_days / 30.44, 1)
        return f"{months} شهر"
    else:
        years = round(age_days / 365.25, 1)
        return f"{years} سنة"

@app.route('/')
def dashboard():
    """الصفحة الرئيسية - لوحة التحكم"""
    total_cows = Cow.query.count()
    lactating_cows = Cow.query.filter(Cow.lactation_period > 0).count()
    pregnant_cows = Cow.query.filter(Cow.pregnancy_status == True).count()
    
    # إجمالي إنتاج الحليب اليوم
    today = date.today()
    today_milk = db.session.query(db.func.sum(MilkRecord.total_quantity)).filter(
        MilkRecord.date == today
    ).scalar() or 0
    
    # متوسط إنتاج الحليب لكل بقرة
    avg_milk_per_cow = today_milk / lactating_cows if lactating_cows > 0 else 0
    
    # الأبقار الأكثر إنتاجاً
    top_producers = db.session.query(Cow, db.func.avg(MilkRecord.total_quantity).label('avg_milk'))\
        .join(MilkRecord)\
        .group_by(Cow.id)\
        .order_by(db.func.avg(MilkRecord.total_quantity).desc())\
        .limit(5).all()
    
    return render_template('dashboard.html',
                         total_cows=total_cows,
                         lactating_cows=lactating_cows,
                         pregnant_cows=pregnant_cows,
                         today_milk=today_milk,
                         avg_milk_per_cow=avg_milk_per_cow,
                         top_producers=top_producers)

# --- إدارة الأبقار ---
@app.route('/cows')
def cows_list():
    """قائمة الأبقار"""
    cows = Cow.query.all()
    return render_template('cows_list.html', cows=cows)

@app.route('/cows/add', methods=['GET', 'POST'])
def add_cow():
    """إضافة بقرة جديدة"""
    form = CowForm()
    if form.validate_on_submit():
        cow = Cow(
            name=form.name.data,
            tag_number=form.tag_number.data,
            breed=form.breed.data,
            birth_date=form.birth_date.data,
            weight=form.weight.data,
            health_status=form.health_status.data,
            pregnancy_status=form.pregnancy_status.data,
            lactation_period=form.lactation_period.data,
            daily_milk_production=form.daily_milk_production.data
        )
        db.session.add(cow)
        db.session.commit()
        flash('تم إضافة البقرة بنجاح!', 'success')
        return redirect(url_for('cows_list'))
    return render_template('add_cow.html', form=form)

# --- إدارة مكونات العلف ---
@app.route('/feed-ingredients')
def feed_ingredients_list():
    """قائمة مكونات العلف"""
    ingredients = FeedIngredient.query.all()
    return render_template('feed_ingredients_list.html', ingredients=ingredients)

@app.route('/feed-ingredients/add', methods=['GET', 'POST'])
def add_feed_ingredient():
    """إضافة مكون علف جديد"""
    form = FeedIngredientForm()
    if form.validate_on_submit():
        ingredient = FeedIngredient(
            name=form.name.data,
            protein_percentage=form.protein_percentage.data,
            energy_mcal_kg=form.energy_mcal_kg.data,
            fiber_percentage=form.fiber_percentage.data,
            moisture_percentage=form.moisture_percentage.data,
            price_per_kg=form.price_per_kg.data,
            supplier=form.supplier.data,
            notes=form.notes.data
        )
        db.session.add(ingredient)
        db.session.commit()
        flash('تم إضافة مكون العلف بنجاح!', 'success')
        return redirect(url_for('feed_ingredients_list'))
    return render_template('add_feed_ingredient.html', form=form)

# --- إدارة خلطات العلف ---
@app.route('/feed-mixes')
def feed_mixes_list():
    """قائمة خلطات العلف"""
    mixes = FeedMix.query.all()
    return render_template('feed_mixes_list.html', mixes=mixes)

@app.route('/feed-mixes/add', methods=['GET', 'POST'])
def add_feed_mix():
    """إضافة خلطة علف جديدة"""
    form = FeedMixForm()
    if form.validate_on_submit():
        mix = FeedMix(
            name=form.name.data,
            description=form.description.data,
            target_protein=form.target_protein.data,
            target_energy=form.target_energy.data,
            target_fiber=form.target_fiber.data
        )
        db.session.add(mix)
        db.session.commit()
        flash('تم إضافة خلطة العلف بنجاح! يرجى الآن تكوين مكونات الخلطة.', 'success')
        return redirect(url_for('configure_feed_mix', mix_id=mix.id))
    return render_template('add_feed_mix.html', form=form)

@app.route('/feed-mixes/<int:mix_id>/configure', methods=['GET', 'POST'])
def configure_feed_mix(mix_id):
    """تكوين خلطة العلف"""
    mix = FeedMix.query.get_or_404(mix_id)
    ingredients = FeedIngredient.query.all()
    
    if request.method == 'POST':
        # حذف المكونات السابقة
        FeedMixIngredient.query.filter_by(feed_mix_id=mix_id).delete()
        
        total_percentage = 0
        valid_ingredients = []
        
        for ingredient in ingredients:
            percentage = request.form.get(f'ingredient_{ingredient.id}', type=float)
            if percentage and percentage > 0:
                valid_ingredients.append({
                    'ingredient_id': ingredient.id,
                    'percentage': percentage
                })
                total_percentage += percentage
        
        # التحقق من أن مجموع النسب = 100%
        if abs(total_percentage - 100) > 0.1:
            flash(f'مجموع النسب يجب أن يكون 100%. المجموع الحالي: {total_percentage:.1f}%', 'error')
        elif not valid_ingredients:
            flash('يجب إضافة مكون واحد على الأقل للخلطة.', 'error')
        else:
            # إضافة المكونات الجديدة
            for ingredient_data in valid_ingredients:
                mix_ingredient = FeedMixIngredient(
                    feed_mix_id=mix_id,
                    ingredient_id=ingredient_data['ingredient_id'],
                    percentage=ingredient_data['percentage']
                )
                db.session.add(mix_ingredient)
            
            # حساب القيم الغذائية
            mix.calculate_nutritional_values()
            db.session.commit()
            
            flash('تم حفظ تكوين الخلطة بنجاح!', 'success')
            return redirect(url_for('feed_mix_details', mix_id=mix_id))
    
    # جلب التكوين الحالي
    current_config = {}
    for mix_ingredient in mix.ingredients:
        current_config[mix_ingredient.ingredient_id] = mix_ingredient.percentage
    
    return render_template('configure_feed_mix.html', 
                         mix=mix, 
                         ingredients=ingredients, 
                         current_config=current_config)

@app.route('/feed-mixes/<int:mix_id>')
def feed_mix_details(mix_id):
    """تفاصيل خلطة العلف"""
    mix = FeedMix.query.get_or_404(mix_id)
    return render_template('feed_mix_details.html', mix=mix)

# --- API للبيانات ---
@app.route('/api/milk-production-chart')
def milk_production_chart():
    """بيانات مخطط إنتاج الحليب"""
    records = db.session.query(
        MilkRecord.date,
        db.func.sum(MilkRecord.total_quantity).label('total')
    ).group_by(MilkRecord.date).order_by(MilkRecord.date.desc()).limit(30).all()
    
    data = {
        'dates': [record.date.strftime('%Y-%m-%d') for record in reversed(records)],
        'quantities': [float(record.total) for record in reversed(records)]
    }

    return jsonify(data)

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
    app.run(debug=True)