{% extends "base.html" %}

{% block title %}تفاصيل البقرة - {{ cow.name }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-cow"></i> تفاصيل البقرة: {{ cow.name }}
            </h1>
            <div>
                <a href="{{ url_for('edit_cow', cow_id=cow.id) }}" class="btn btn-warning">
                    <i class="fas fa-edit"></i> تعديل
                </a>
                <a href="{{ url_for('cows_list') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> العودة للقائمة
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle"></i> معلومات البقرة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">اسم البقرة</label>
                            <div class="h5">{{ cow.name }}</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">رقم الأذن</label>
                            <div class="h5 text-primary">{{ cow.tag_number }}</div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">السلالة</label>
                            <div>{{ cow.breed }}</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">العمر</label>
                            <div>{{ cow.birth_date | age }}</div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">تاريخ الولادة</label>
                            <div>{{ cow.birth_date.strftime('%Y-%m-%d') }}</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">الوزن</label>
                            <div class="h5 text-success">{{ cow.weight }} كيلو</div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">تاريخ التسجيل</label>
                            <div>{{ cow.created_at.strftime('%Y-%m-%d %H:%M') }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line"></i> سجلات الإنتاج والتغذية
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-tint"></i> سجلات الحليب</h6>
                        {% if cow.milk_records %}
                            <p class="text-success">{{ cow.milk_records|length }} سجل حليب</p>
                            <small class="text-muted">آخر سجل: {{ cow.milk_records[-1].date.strftime('%Y-%m-%d') if cow.milk_records else 'لا يوجد' }}</small>
                        {% else %}
                            <p class="text-muted">لا توجد سجلات حليب</p>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-utensils"></i> سجلات التغذية</h6>
                        {% if cow.feeding_records %}
                            <p class="text-success">{{ cow.feeding_records|length }} سجل تغذية</p>
                            <small class="text-muted">آخر سجل: {{ cow.feeding_records[-1].date.strftime('%Y-%m-%d') if cow.feeding_records else 'لا يوجد' }}</small>
                        {% else %}
                            <p class="text-muted">لا توجد سجلات تغذية</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-heartbeat"></i> الحالة الصحية والإنتاج
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label">الحالة الصحية</label>
                    <div>
                        {% if cow.health_status == 'سليمة' %}
                            <span class="badge bg-success fs-6">{{ cow.health_status }}</span>
                        {% elif cow.health_status == 'مريضة' %}
                            <span class="badge bg-danger fs-6">{{ cow.health_status }}</span>
                        {% else %}
                            <span class="badge bg-warning fs-6">{{ cow.health_status }}</span>
                        {% endif %}
                    </div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">حالة الحمل</label>
                    <div>
                        {% if cow.pregnancy_status %}
                            <span class="badge bg-info fs-6">حامل</span>
                        {% else %}
                            <span class="badge bg-secondary fs-6">غير حامل</span>
                        {% endif %}
                    </div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">فترة الإدرار</label>
                    <div>
                        {% if cow.lactation_period > 0 %}
                            <div class="h5 text-primary">{{ cow.lactation_period }} يوم</div>
                            <div class="progress">
                                <div class="progress-bar" style="width: {{ (cow.lactation_period / 305) * 100 }}%"></div>
                            </div>
                            <small class="text-muted">من أصل 305 يوم (دورة كاملة)</small>
                        {% else %}
                            <span class="text-muted">البقرة جافة</span>
                        {% endif %}
                    </div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">إنتاج الحليب اليومي</label>
                    <div>
                        {% if cow.daily_milk_production > 0 %}
                            <div class="h4 text-success">{{ cow.daily_milk_production }} لتر</div>
                            {% if cow.daily_milk_production > 30 %}
                                <span class="badge bg-success">إنتاج عالي</span>
                            {% elif cow.daily_milk_production > 20 %}
                                <span class="badge bg-warning">إنتاج متوسط</span>
                            {% else %}
                                <span class="badge bg-info">إنتاج منخفض</span>
                            {% endif %}
                        {% else %}
                            <span class="text-muted">غير منتجة</span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calculator"></i> إحصائيات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label">العمر بالأيام</label>
                    {% set age_days = (moment.utcnow().date() - cow.birth_date).days if moment is defined else 0 %}
                    <div class="h6">{{ age_days }} يوم</div>
                </div>
                
                {% if cow.lactation_period > 0 %}
                <div class="mb-3">
                    <label class="form-label">الإنتاج المتوقع هذا الشهر</label>
                    <div class="h6 text-success">{{ (cow.daily_milk_production * 30)|round(1) }} لتر</div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">الإنتاج المتوقع في الدورة</label>
                    <div class="h6 text-info">{{ (cow.daily_milk_production * (305 - cow.lactation_period))|round(1) }} لتر</div>
                </div>
                {% endif %}
                
                <div class="mb-3">
                    <label class="form-label">تصنيف البقرة</label>
                    <div>
                        {% if cow.daily_milk_production > 25 %}
                            <span class="badge bg-success">بقرة ممتازة</span>
                        {% elif cow.daily_milk_production > 15 %}
                            <span class="badge bg-primary">بقرة جيدة</span>
                        {% elif cow.daily_milk_production > 0 %}
                            <span class="badge bg-warning">بقرة متوسطة</span>
                        {% else %}
                            <span class="badge bg-secondary">بقرة جافة</span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
