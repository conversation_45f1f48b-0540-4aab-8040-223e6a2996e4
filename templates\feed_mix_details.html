{% extends "base.html" %}

{% block title %}تفاصيل خلطة العلف - {{ mix.name }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-blender"></i> تفاصيل خلطة العلف: {{ mix.name }}
            </h1>
            <div>
                <a href="{{ url_for('configure_feed_mix', mix_id=mix.id) }}" class="btn btn-warning">
                    <i class="fas fa-cogs"></i> تكوين الخلطة
                </a>
                <a href="{{ url_for('feed_mixes_list') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> العودة للقائمة
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle"></i> معلومات الخلطة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">اسم الخلطة</label>
                            <div class="h5">{{ mix.name }}</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">تاريخ الإنشاء</label>
                            <div class="h6">{{ mix.created_at.strftime('%Y-%m-%d %H:%M') }}</div>
                        </div>
                    </div>
                </div>
                
                {% if mix.description %}
                <div class="mb-3">
                    <label class="form-label text-muted">الوصف</label>
                    <div>{{ mix.description }}</div>
                </div>
                {% endif %}
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list"></i> مكونات الخلطة
                </h5>
            </div>
            <div class="card-body">
                {% if mix.ingredients %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead class="table-light">
                                <tr>
                                    <th>المكون</th>
                                    <th>النسبة (%)</th>
                                    <th>البروتين (%)</th>
                                    <th>الطاقة</th>
                                    <th>الألياف (%)</th>
                                    <th>السعر/كيلو</th>
                                    <th>المساهمة في التكلفة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for mix_ingredient in mix.ingredients %}
                                <tr>
                                    <td><strong>{{ mix_ingredient.ingredient.name }}</strong></td>
                                    <td>{{ mix_ingredient.percentage }}%</td>
                                    <td>{{ mix_ingredient.ingredient.protein_percentage }}%</td>
                                    <td>{{ mix_ingredient.ingredient.energy_mcal_kg }}</td>
                                    <td>{{ mix_ingredient.ingredient.fiber_percentage }}%</td>
                                    <td>{{ mix_ingredient.ingredient.price_per_kg }} ريال</td>
                                    <td>{{ "%.2f"|format((mix_ingredient.percentage / 100) * mix_ingredient.ingredient.price_per_kg) }} ريال</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-exclamation-circle fa-2x text-warning mb-3"></i>
                        <h5>لم يتم تكوين مكونات الخلطة بعد</h5>
                        <p class="text-muted">يرجى تكوين مكونات الخلطة ونسبها</p>
                        <a href="{{ url_for('configure_feed_mix', mix_id=mix.id) }}" class="btn btn-primary">
                            <i class="fas fa-cogs"></i> تكوين الخلطة
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-target"></i> الأهداف المطلوبة
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label">البروتين المستهدف</label>
                    <div class="h5 text-primary">{{ mix.target_protein }}%</div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">الطاقة المستهدفة</label>
                    <div class="h5 text-success">{{ mix.target_energy }} ميجا كالوري/كيلو</div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">الألياف المستهدفة</label>
                    <div class="h5 text-info">{{ mix.target_fiber }}%</div>
                </div>
            </div>
        </div>
        
        {% if mix.calculated_protein > 0 %}
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calculator"></i> القيم المحسوبة
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label">البروتين الفعلي</label>
                    <div class="h5">
                        {% set protein_diff = abs_filter(mix.calculated_protein - mix.target_protein) %}
                        <span class="badge bg-{{ 'success' if protein_diff <= 1 else 'warning' }} fs-6">
                            {{ mix.calculated_protein }}%
                        </span>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label">الطاقة الفعلية</label>
                    <div class="h5">
                        {% set energy_diff = abs_filter(mix.calculated_energy - mix.target_energy) %}
                        <span class="badge bg-{{ 'success' if energy_diff <= 0.2 else 'warning' }} fs-6">
                            {{ mix.calculated_energy }} ميجا كالوري/كيلو
                        </span>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label">الألياف الفعلية</label>
                    <div class="h5">
                        {% set fiber_diff = abs_filter(mix.calculated_fiber - mix.target_fiber) %}
                        <span class="badge bg-{{ 'success' if fiber_diff <= 1 else 'warning' }} fs-6">
                            {{ mix.calculated_fiber }}%
                        </span>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">التكلفة الإجمالية</label>
                    <div class="h5 text-warning">{{ mix.total_cost_per_kg }} ريال/كيلو</div>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie"></i> تقييم الخلطة
                </h5>
            </div>
            <div class="card-body">
                {% set protein_diff = abs_filter(mix.calculated_protein - mix.target_protein) %}
                {% set energy_diff = abs_filter(mix.calculated_energy - mix.target_energy) %}
                {% set fiber_diff = abs_filter(mix.calculated_fiber - mix.target_fiber) %}
                
                <div class="mb-2">
                    <small class="text-muted">دقة البروتين</small>
                    <div class="progress">
                        <div class="progress-bar bg-{{ 'success' if protein_diff <= 1 else 'warning' if protein_diff <= 2 else 'danger' }}" 
                             style="width: {{ 100 - (protein_diff * 10) if protein_diff <= 10 else 0 }}%">
                        </div>
                    </div>
                </div>
                
                <div class="mb-2">
                    <small class="text-muted">دقة الطاقة</small>
                    <div class="progress">
                        <div class="progress-bar bg-{{ 'success' if energy_diff <= 0.2 else 'warning' if energy_diff <= 0.5 else 'danger' }}" 
                             style="width: {{ 100 - (energy_diff * 50) if energy_diff <= 2 else 0 }}%">
                        </div>
                    </div>
                </div>
                
                <div class="mb-2">
                    <small class="text-muted">دقة الألياف</small>
                    <div class="progress">
                        <div class="progress-bar bg-{{ 'success' if fiber_diff <= 1 else 'warning' if fiber_diff <= 2 else 'danger' }}" 
                             style="width: {{ 100 - (fiber_diff * 10) if fiber_diff <= 10 else 0 }}%">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
