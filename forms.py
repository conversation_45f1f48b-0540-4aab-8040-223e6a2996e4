# forms.py
from flask_wtf import FlaskForm
from wtforms import StringField, FloatField, IntegerField, SelectField, TextAreaField, DateField, BooleanField
from wtforms.validators import DataRequired, NumberRange, Length, ValidationError
from models import FeedIngredient, Cow
from predefined_ingredients import get_ingredient_choices, get_ingredient_data

class CowForm(FlaskForm):
    """استمارة إضافة/تعديل البقرة"""
    name = StringField('اسم البقرة', validators=[DataRequired(), Length(min=2, max=100)])
    tag_number = StringField('رقم الأذن', validators=[DataRequired(), Length(min=1, max=50)])
    breed = SelectField('السلالة', choices=[
        ('هولشتاين', 'هولشتاين'),
        ('جيرسي', 'جيرسي'),
        ('براون سويس', 'براون سويس'),
        ('أيرشاير', 'أيرشاير'),
        ('محلية', 'محلية')
    ], validators=[DataRequired()])
    birth_date = DateField('تاريخ الولادة', validators=[DataRequired()])
    weight = FloatField('الوزن (كيلو)', validators=[DataRequired(), NumberRange(min=100, max=1000)])
    health_status = SelectField('الحالة الصحية', choices=[
        ('سليمة', 'سليمة'),
        ('مريضة', 'مريضة'),
        ('تحت العلاج', 'تحت العلاج')
    ])
    pregnancy_status = BooleanField('حامل')
    lactation_period = IntegerField('فترة الإدرار (أيام)', default=0, validators=[NumberRange(min=0)])
    daily_milk_production = FloatField('إنتاج الحليب اليومي (لتر)', default=0.0, validators=[NumberRange(min=0)])

    def validate_tag_number(self, tag_number):
        # التحقق من عدم وجود بقرة أخرى بنفس رقم الأذن (باستثناء البقرة الحالية في حالة التعديل)
        cow = Cow.query.filter_by(tag_number=tag_number.data).first()
        if cow and (not hasattr(self, '_obj') or cow.id != self._obj.id):
            raise ValidationError('رقم الأذن موجود بالفعل. يرجى اختيار رقم آخر.')

    def __init__(self, *args, **kwargs):
        # حفظ الكائن الحالي للتحقق من التعديل
        self._obj = kwargs.pop('obj', None)
        super(CowForm, self).__init__(*args, **kwargs)

class FeedIngredientForm(FlaskForm):
    """استمارة مكونات العلف - للتعديل"""
    name = StringField('اسم المكون', validators=[DataRequired(), Length(min=2, max=100)])
    protein_percentage = FloatField('نسبة البروتين (%)', validators=[DataRequired(), NumberRange(min=0, max=100)])
    energy_mcal_kg = FloatField('الطاقة (ميجا كالوري/كيلو)', validators=[DataRequired(), NumberRange(min=0, max=10)])
    fiber_percentage = FloatField('نسبة الألياف (%)', validators=[DataRequired(), NumberRange(min=0, max=100)])
    moisture_percentage = FloatField('نسبة الرطوبة (%)', default=10.0, validators=[NumberRange(min=0, max=100)])
    price_per_kg = FloatField('سعر الكيلو', validators=[DataRequired(), NumberRange(min=0)])
    supplier = StringField('المورد', validators=[Length(max=100)])
    notes = TextAreaField('ملاحظات')

    def validate_name(self, name):
        # التحقق من عدم وجود مكون آخر بنفس الاسم (باستثناء المكون الحالي في حالة التعديل)
        ingredient = FeedIngredient.query.filter_by(name=name.data).first()
        if ingredient and (not hasattr(self, '_obj') or ingredient.id != self._obj.id):
            raise ValidationError('اسم المكون موجود بالفعل. يرجى اختيار اسم آخر.')

    def __init__(self, *args, **kwargs):
        # حفظ الكائن الحالي للتحقق من التعديل
        self._obj = kwargs.pop('obj', None)
        super(FeedIngredientForm, self).__init__(*args, **kwargs)

class PredefinedFeedIngredientForm(FlaskForm):
    """استمارة إضافة مكون علف من القائمة المحددة مسبقاً"""
    predefined_ingredient = SelectField('اختر مكون العلف',
                                      choices=[],
                                      validators=[DataRequired(message='يرجى اختيار مكون من القائمة')])
    price_per_kg = FloatField('سعر الكيلو (ريال)', validators=[DataRequired(), NumberRange(min=0)])
    supplier = StringField('المورد', validators=[Length(max=100)])
    notes = TextAreaField('ملاحظات إضافية')

    def __init__(self, *args, **kwargs):
        super(PredefinedFeedIngredientForm, self).__init__(*args, **kwargs)
        self.predefined_ingredient.choices = get_ingredient_choices()

    def validate_predefined_ingredient(self, predefined_ingredient):
        if predefined_ingredient.data:
            # التحقق من عدم وجود المكون مسبقاً في قاعدة البيانات
            existing = FeedIngredient.query.filter_by(name=predefined_ingredient.data).first()
            if existing:
                raise ValidationError(f'المكون "{predefined_ingredient.data}" موجود بالفعل في قاعدة البيانات.')

            # التحقق من وجود المكون في القائمة المحددة مسبقاً
            ingredient_data = get_ingredient_data(predefined_ingredient.data)
            if not ingredient_data:
                raise ValidationError('المكون المختار غير صحيح.')

class FeedMixForm(FlaskForm):
    """استمارة خلطات العلف"""
    name = StringField('اسم الخلطة', validators=[DataRequired(), Length(min=2, max=100)])
    description = TextAreaField('الوصف')
    target_protein = FloatField('البروتين المستهدف (%)', validators=[DataRequired(), NumberRange(min=0, max=50)])
    target_energy = FloatField('الطاقة المستهدفة (ميجا كالوري/كيلو)', validators=[DataRequired(), NumberRange(min=0, max=5)])
    target_fiber = FloatField('الألياف المستهدفة (%)', validators=[DataRequired(), NumberRange(min=0, max=50)])
