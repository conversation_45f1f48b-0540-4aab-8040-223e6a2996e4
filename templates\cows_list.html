{% extends "base.html" %}

{% block title %}قائمة الأبقار - نظام إدارة مزرعة الألبان{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-cow"></i> قائمة الأبقار
            </h1>
            <a href="{{ url_for('add_cow') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> إضافة بقرة جديدة
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                {% if cows %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>الاسم</th>
                                    <th>رقم الأذن</th>
                                    <th>السلالة</th>
                                    <th>العمر</th>
                                    <th>الوزن (كيلو)</th>
                                    <th>الحالة الصحية</th>
                                    <th>حامل</th>
                                    <th>فترة الإدرار</th>
                                    <th>الإنتاج اليومي</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for cow in cows %}
                                <tr>
                                    <td><strong>{{ cow.name }}</strong></td>
                                    <td>{{ cow.tag_number }}</td>
                                    <td>{{ cow.breed }}</td>
                                    <td>
                                        {% set age_days = (moment().date() - cow.birth_date).days %}
                                        {% if age_days < 365 %}
                                            {{ (age_days / 30.44) | round(1) }} شهر
                                        {% else %}
                                            {{ (age_days / 365.25) | round(1) }} سنة
                                        {% endif %}
                                    </td>
                                    <td>{{ cow.weight }}</td>
                                    <td>
                                        {% if cow.health_status == 'سليمة' %}
                                            <span class="badge bg-success">{{ cow.health_status }}</span>
                                        {% elif cow.health_status == 'مريضة' %}
                                            <span class="badge bg-danger">{{ cow.health_status }}</span>
                                        {% else %}
                                            <span class="badge bg-warning">{{ cow.health_status }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if cow.pregnancy_status %}
                                            <span class="badge bg-info">نعم</span>
                                        {% else %}
                                            <span class="badge bg-secondary">لا</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if cow.lactation_period > 0 %}
                                            {{ cow.lactation_period }} يوم
                                        {% else %}
                                            <span class="text-muted">جافة</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if cow.daily_milk_production > 0 %}
                                            {{ cow.daily_milk_production }} لتر
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <button type="button" class="btn btn-outline-primary" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-warning" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-danger" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-cow fa-3x text-muted mb-3"></i>
                        <h4 class="text-muted">لا توجد أبقار مسجلة</h4>
                        <p class="text-muted">ابدأ بإضافة أول بقرة في المزرعة</p>
                        <a href="{{ url_for('add_cow') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> إضافة بقرة جديدة
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
{% endblock %}
