# add_sample_data.py
from app import app, db
from models import FeedIngredient

def add_sample_feed_ingredients():
    """إضافة مكونات العلف الأساسية مع قيمها الغذائية"""
    
    # قائمة مكونات العلف مع قيمها الغذائية الحقيقية
    ingredients = [
        # الحبوب
        {
            'name': 'الذرة الصفراء',
            'protein_percentage': 8.5,
            'energy_mcal_kg': 3.3,
            'fiber_percentage': 2.5,
            'moisture_percentage': 14.0,
            'price_per_kg': 1.8,
            'supplier': 'مؤسسة الحبوب',
            'notes': 'مصدر ممتاز للطاقة، غني بالكربوهيدرات'
        },
        {
            'name': 'الشعير',
            'protein_percentage': 11.5,
            'energy_mcal_kg': 2.9,
            'fiber_percentage': 5.5,
            'moisture_percentage': 12.0,
            'price_per_kg': 1.5,
            'supplier': 'مؤسسة الحبوب',
            'notes': 'حبوب ممتازة للأبقار، سهلة الهضم'
        },
        {
            'name': 'القمح',
            'protein_percentage': 12.0,
            'energy_mcal_kg': 3.2,
            'fiber_percentage': 3.0,
            'moisture_percentage': 13.0,
            'price_per_kg': 2.0,
            'supplier': 'مؤسسة الحبوب',
            'notes': 'مصدر جيد للبروتين والطاقة'
        },
        
        # البقوليات ومصادر البروتين
        {
            'name': 'كسبة فول الصويا',
            'protein_percentage': 44.0,
            'energy_mcal_kg': 2.2,
            'fiber_percentage': 7.0,
            'moisture_percentage': 12.0,
            'price_per_kg': 3.5,
            'supplier': 'شركة البروتين',
            'notes': 'أفضل مصدر للبروتين النباتي، غني بالأحماض الأمينية'
        },
        {
            'name': 'كسبة بذرة القطن',
            'protein_percentage': 38.0,
            'energy_mcal_kg': 1.9,
            'fiber_percentage': 12.0,
            'moisture_percentage': 10.0,
            'price_per_kg': 2.8,
            'supplier': 'مصانع الزيوت',
            'notes': 'مصدر جيد للبروتين، أقل تكلفة من الصويا'
        },
        {
            'name': 'كسبة عباد الشمس',
            'protein_percentage': 28.0,
            'energy_mcal_kg': 2.0,
            'fiber_percentage': 25.0,
            'moisture_percentage': 11.0,
            'price_per_kg': 2.2,
            'supplier': 'مصانع الزيوت',
            'notes': 'مصدر متوسط للبروتين، عالي الألياف'
        },
        
        # النخالة والمخلفات
        {
            'name': 'نخالة القمح',
            'protein_percentage': 15.5,
            'energy_mcal_kg': 1.8,
            'fiber_percentage': 42.0,
            'moisture_percentage': 12.0,
            'price_per_kg': 1.2,
            'supplier': 'مطاحن الدقيق',
            'notes': 'غنية بالألياف والفوسفور، مفيدة للهضم'
        },
        {
            'name': 'نخالة الأرز',
            'protein_percentage': 13.0,
            'energy_mcal_kg': 2.5,
            'fiber_percentage': 20.0,
            'moisture_percentage': 10.0,
            'price_per_kg': 1.0,
            'supplier': 'مطاحن الأرز',
            'notes': 'مصدر اقتصادي للطاقة والألياف'
        },
        
        # الأعلاف الخضراء المجففة
        {
            'name': 'دريس البرسيم',
            'protein_percentage': 18.0,
            'energy_mcal_kg': 2.2,
            'fiber_percentage': 28.0,
            'moisture_percentage': 15.0,
            'price_per_kg': 1.8,
            'supplier': 'مزارع الأعلاف',
            'notes': 'علف ممتاز، غني بالبروتين والكالسيوم'
        },
        {
            'name': 'تبن القمح',
            'protein_percentage': 4.0,
            'energy_mcal_kg': 1.2,
            'fiber_percentage': 45.0,
            'moisture_percentage': 12.0,
            'price_per_kg': 0.5,
            'supplier': 'المزارع المحلية',
            'notes': 'مصدر رخيص للألياف، يحتاج لمكملات بروتين'
        },
        
        # المكملات المعدنية والفيتامينات
        {
            'name': 'حجر الجير (كربونات الكالسيوم)',
            'protein_percentage': 0.0,
            'energy_mcal_kg': 0.0,
            'fiber_percentage': 0.0,
            'moisture_percentage': 2.0,
            'price_per_kg': 0.8,
            'supplier': 'شركة المعادن',
            'notes': 'مصدر الكالسيوم، ضروري لتكوين العظام وإنتاج الحليب'
        },
        {
            'name': 'ملح الطعام (كلوريد الصوديوم)',
            'protein_percentage': 0.0,
            'energy_mcal_kg': 0.0,
            'fiber_percentage': 0.0,
            'moisture_percentage': 1.0,
            'price_per_kg': 1.5,
            'supplier': 'شركة الأملاح',
            'notes': 'ضروري لتوازن السوائل والشهية'
        },
        {
            'name': 'فوسفات ثنائي الكالسيوم',
            'protein_percentage': 0.0,
            'energy_mcal_kg': 0.0,
            'fiber_percentage': 0.0,
            'moisture_percentage': 2.0,
            'price_per_kg': 4.5,
            'supplier': 'شركة المعادن',
            'notes': 'مصدر الفوسفور والكالسيوم، مهم لتكوين العظام'
        },
        {
            'name': 'خليط الفيتامينات والمعادن',
            'protein_percentage': 0.0,
            'energy_mcal_kg': 0.0,
            'fiber_percentage': 0.0,
            'moisture_percentage': 5.0,
            'price_per_kg': 25.0,
            'supplier': 'شركة التغذية المتخصصة',
            'notes': 'خليط متكامل من الفيتامينات A, D, E والمعادن النادرة'
        },
        
        # مصادر الدهون
        {
            'name': 'زيت النخيل',
            'protein_percentage': 0.0,
            'energy_mcal_kg': 8.5,
            'fiber_percentage': 0.0,
            'moisture_percentage': 1.0,
            'price_per_kg': 6.0,
            'supplier': 'شركة الزيوت',
            'notes': 'مصدر مركز للطاقة، يحسن من جودة الحليب'
        },
        
        # مكونات أخرى
        {
            'name': 'دبس السكر (المولاس)',
            'protein_percentage': 3.0,
            'energy_mcal_kg': 2.8,
            'fiber_percentage': 0.0,
            'moisture_percentage': 25.0,
            'price_per_kg': 2.5,
            'supplier': 'مصانع السكر',
            'notes': 'يحسن الطعم ومصدر سريع للطاقة'
        },
        {
            'name': 'خميرة البيرة المجففة',
            'protein_percentage': 45.0,
            'energy_mcal_kg': 3.0,
            'fiber_percentage': 2.0,
            'moisture_percentage': 8.0,
            'price_per_kg': 8.0,
            'supplier': 'شركة المكملات',
            'notes': 'غنية بفيتامينات B والبروتين عالي الجودة'
        }
    ]
    
    with app.app_context():
        # التحقق من وجود البيانات مسبقاً
        existing_count = FeedIngredient.query.count()
        if existing_count > 0:
            print(f"يوجد بالفعل {existing_count} مكون في قاعدة البيانات.")
            response = input("هل تريد إضافة المكونات الجديدة؟ (y/n): ")
            if response.lower() != 'y':
                return
        
        # إضافة المكونات
        added_count = 0
        for ingredient_data in ingredients:
            # التحقق من عدم وجود المكون مسبقاً
            existing = FeedIngredient.query.filter_by(name=ingredient_data['name']).first()
            if not existing:
                ingredient = FeedIngredient(**ingredient_data)
                db.session.add(ingredient)
                added_count += 1
                print(f"تم إضافة: {ingredient_data['name']}")
            else:
                print(f"موجود مسبقاً: {ingredient_data['name']}")
        
        # حفظ التغييرات
        try:
            db.session.commit()
            print(f"\n✅ تم إضافة {added_count} مكون علف بنجاح!")
            print(f"إجمالي المكونات في قاعدة البيانات: {FeedIngredient.query.count()}")
        except Exception as e:
            db.session.rollback()
            print(f"❌ خطأ في حفظ البيانات: {e}")

if __name__ == '__main__':
    add_sample_feed_ingredients()
