# add_sample_cows.py
from app import app, db
from models import Cow
from datetime import date, timedelta
import random

def add_sample_cows():
    """إضافة أبقار نموذجية للمزرعة"""
    
    # أسماء الأبقار
    cow_names = [
        'فاطمة', 'عائشة', 'خديجة', 'زينب', 'مريم', 'سارة', 'هند', 'أسماء',
        'حفصة', 'أم كلثوم', 'رقية', 'صفية', 'جويرية', 'ميمونة', 'زينب الثانية',
        'البركة', 'النجمة', 'الدرة', 'الياقوتة', 'الزهرة'
    ]
    
    # السلالات المتاحة
    breeds = ['هولشتاين', 'جيرسي', 'براون سويس', 'أيرشاير', 'محلية']
    
    # الحالات الصحية
    health_statuses = ['سليمة', 'سليمة', 'سليمة', 'سليمة', 'مريضة', 'تحت العلاج']
    
    cows_data = []
    
    for i, name in enumerate(cow_names):
        # حساب تاريخ ولادة عشوائي (بين 2-8 سنوات)
        age_days = random.randint(730, 2920)  # 2-8 سنوات
        birth_date = date.today() - timedelta(days=age_days)
        
        # اختيار سلالة عشوائية
        breed = random.choice(breeds)
        
        # تحديد الوزن حسب السلالة
        if breed == 'هولشتاين':
            weight = random.randint(550, 700)
        elif breed == 'جيرسي':
            weight = random.randint(350, 450)
        elif breed == 'براون سويس':
            weight = random.randint(500, 650)
        elif breed == 'أيرشاير':
            weight = random.randint(450, 550)
        else:  # محلية
            weight = random.randint(400, 500)
        
        # تحديد الحالة الصحية
        health_status = random.choice(health_statuses)
        
        # تحديد حالة الحمل (30% احتمال)
        pregnancy_status = random.choice([True, False, False, False])
        
        # تحديد فترة الإدرار وإنتاج الحليب
        if age_days > 730:  # أكبر من سنتين
            if pregnancy_status:
                # إذا كانت حامل، قد تكون في فترة جفاف
                lactation_period = random.choice([0, 0, random.randint(50, 200)])
            else:
                lactation_period = random.randint(0, 305)
            
            if lactation_period > 0:
                # إنتاج الحليب حسب السلالة وفترة الإدرار
                if breed == 'هولشتاين':
                    base_production = random.randint(25, 40)
                elif breed == 'جيرسي':
                    base_production = random.randint(18, 28)
                elif breed == 'براون سويس':
                    base_production = random.randint(22, 35)
                elif breed == 'أيرشاير':
                    base_production = random.randint(20, 30)
                else:  # محلية
                    base_production = random.randint(12, 20)
                
                # تقليل الإنتاج إذا كانت في نهاية فترة الإدرار
                if lactation_period > 250:
                    daily_milk_production = base_production * 0.6
                elif lactation_period > 200:
                    daily_milk_production = base_production * 0.8
                else:
                    daily_milk_production = base_production
                
                # تقليل الإنتاج إذا كانت مريضة
                if health_status != 'سليمة':
                    daily_milk_production *= 0.7
                    
                daily_milk_production = round(daily_milk_production, 1)
            else:
                daily_milk_production = 0.0
        else:
            # عجول صغيرة
            lactation_period = 0
            daily_milk_production = 0.0
            pregnancy_status = False
        
        cow_data = {
            'name': name,
            'tag_number': f"C{i+1:03d}",  # C001, C002, etc.
            'breed': breed,
            'birth_date': birth_date,
            'weight': weight,
            'health_status': health_status,
            'pregnancy_status': pregnancy_status,
            'lactation_period': lactation_period,
            'daily_milk_production': daily_milk_production
        }
        
        cows_data.append(cow_data)
    
    with app.app_context():
        # التحقق من وجود أبقار مسبقاً
        existing_count = Cow.query.count()
        if existing_count > 0:
            print(f"يوجد بالفعل {existing_count} بقرة في قاعدة البيانات.")
            response = input("هل تريد إضافة الأبقار الجديدة؟ (y/n): ")
            if response.lower() != 'y':
                return
        
        added_count = 0
        for cow_data in cows_data:
            # التحقق من عدم وجود البقرة مسبقاً
            existing_cow = Cow.query.filter_by(tag_number=cow_data['tag_number']).first()
            if not existing_cow:
                cow = Cow(**cow_data)
                db.session.add(cow)
                added_count += 1
                
                # عرض معلومات البقرة
                status = "منتجة" if cow_data['daily_milk_production'] > 0 else "جافة"
                pregnancy = "حامل" if cow_data['pregnancy_status'] else "غير حامل"
                print(f"تم إضافة: {cow_data['name']} ({cow_data['tag_number']}) - {cow_data['breed']} - {status} - {pregnancy}")
            else:
                print(f"موجودة مسبقاً: {cow_data['name']} ({cow_data['tag_number']})")
        
        # حفظ التغييرات
        try:
            db.session.commit()
            print(f"\n✅ تم إضافة {added_count} بقرة بنجاح!")
            print(f"إجمالي الأبقار في قاعدة البيانات: {Cow.query.count()}")
            
            # عرض إحصائيات المزرعة
            total_cows = Cow.query.count()
            lactating_cows = Cow.query.filter(Cow.lactation_period > 0).count()
            pregnant_cows = Cow.query.filter(Cow.pregnancy_status == True).count()
            total_production = db.session.query(db.func.sum(Cow.daily_milk_production)).scalar() or 0
            
            print(f"\n📊 إحصائيات المزرعة:")
            print(f"   إجمالي الأبقار: {total_cows}")
            print(f"   الأبقار المنتجة: {lactating_cows}")
            print(f"   الأبقار الحوامل: {pregnant_cows}")
            print(f"   إجمالي الإنتاج اليومي: {total_production} لتر")
            if lactating_cows > 0:
                print(f"   متوسط الإنتاج لكل بقرة منتجة: {total_production/lactating_cows:.1f} لتر")
                
        except Exception as e:
            db.session.rollback()
            print(f"❌ خطأ في حفظ البيانات: {e}")

if __name__ == '__main__':
    add_sample_cows()
