# add_sample_mixes.py
from app import app, db
from models import FeedMix, FeedIngredient, FeedMixIngredient

def add_sample_feed_mixes():
    """إضافة خلطات علف نموذجية للأبقار"""
    
    # خلطات العلف النموذجية
    mixes_data = [
        {
            'name': 'خلطة الأبقار عالية الإنتاج',
            'description': 'خلطة مخصصة للأبقار التي تنتج أكثر من 25 لتر حليب يومياً',
            'target_protein': 18.0,
            'target_energy': 2.8,
            'target_fiber': 15.0,
            'ingredients': {
                'الذرة الصفراء': 35.0,
                'كسبة فول الصويا': 20.0,
                'نخالة القمح': 15.0,
                'الشعير': 15.0,
                'دريس البرسيم': 10.0,
                'دبس السكر (المولاس)': 2.0,
                'حجر الجير (كربونات الكالسيوم)': 1.5,
                'ملح الطعام (كلوريد الصوديوم)': 0.5,
                'خليط الفيتامينات والمعادن': 1.0
            }
        },
        {
            'name': 'خلطة الأبقار متوسطة الإنتاج',
            'description': 'خلطة مناسبة للأبقار التي تنتج 15-25 لتر حليب يومياً',
            'target_protein': 16.0,
            'target_energy': 2.5,
            'target_fiber': 18.0,
            'ingredients': {
                'الذرة الصفراء': 30.0,
                'الشعير': 25.0,
                'كسبة فول الصويا': 15.0,
                'نخالة القمح': 12.0,
                'دريس البرسيم': 15.0,
                'دبس السكر (المولاس)': 1.5,
                'حجر الجير (كربونات الكالسيوم)': 1.0,
                'ملح الطعام (كلوريد الصوديوم)': 0.5
            }
        },
        {
            'name': 'خلطة الأبقار الجافة',
            'description': 'خلطة للأبقار في فترة الجفاف (غير منتجة للحليب)',
            'target_protein': 12.0,
            'target_energy': 2.0,
            'target_fiber': 25.0,
            'ingredients': {
                'تبن القمح': 40.0,
                'الشعير': 20.0,
                'نخالة الأرز': 15.0,
                'دريس البرسيم': 20.0,
                'كسبة عباد الشمس': 3.0,
                'حجر الجير (كربونات الكالسيوم)': 1.0,
                'ملح الطعام (كلوريد الصوديوم)': 0.5,
                'خليط الفيتامينات والمعادن': 0.5
            }
        },
        {
            'name': 'خلطة العجول النامية',
            'description': 'خلطة مخصصة للعجول من عمر 6-18 شهر',
            'target_protein': 16.5,
            'target_energy': 2.7,
            'target_fiber': 12.0,
            'ingredients': {
                'الذرة الصفراء': 40.0,
                'كسبة فول الصويا': 18.0,
                'الشعير': 20.0,
                'نخالة القمح': 10.0,
                'دريس البرسيم': 8.0,
                'دبس السكر (المولاس)': 2.0,
                'حجر الجير (كربونات الكالسيوم)': 1.0,
                'ملح الطعام (كلوريد الصوديوم)': 0.5,
                'خليط الفيتامينات والمعادن': 0.5
            }
        },
        {
            'name': 'خلطة اقتصادية',
            'description': 'خلطة منخفضة التكلفة للأبقار منخفضة الإنتاج',
            'target_protein': 14.0,
            'target_energy': 2.2,
            'target_fiber': 22.0,
            'ingredients': {
                'نخالة الأرز': 25.0,
                'الشعير': 25.0,
                'تبن القمح': 20.0,
                'كسبة بذرة القطن': 12.0,
                'دريس البرسيم': 15.0,
                'دبس السكر (المولاس)': 1.5,
                'حجر الجير (كربونات الكالسيوم)': 1.0,
                'ملح الطعام (كلوريد الصوديوم)': 0.5
            }
        }
    ]
    
    with app.app_context():
        # التحقق من وجود خلطات مسبقاً
        existing_count = FeedMix.query.count()
        if existing_count > 0:
            print(f"يوجد بالفعل {existing_count} خلطة في قاعدة البيانات.")
            response = input("هل تريد إضافة الخلطات الجديدة؟ (y/n): ")
            if response.lower() != 'y':
                return
        
        added_count = 0
        for mix_data in mixes_data:
            # التحقق من عدم وجود الخلطة مسبقاً
            existing_mix = FeedMix.query.filter_by(name=mix_data['name']).first()
            if existing_mix:
                print(f"موجودة مسبقاً: {mix_data['name']}")
                continue
            
            # إنشاء الخلطة
            mix = FeedMix(
                name=mix_data['name'],
                description=mix_data['description'],
                target_protein=mix_data['target_protein'],
                target_energy=mix_data['target_energy'],
                target_fiber=mix_data['target_fiber']
            )
            db.session.add(mix)
            db.session.flush()  # للحصول على ID الخلطة
            
            # إضافة مكونات الخلطة
            total_percentage = 0
            for ingredient_name, percentage in mix_data['ingredients'].items():
                ingredient = FeedIngredient.query.filter_by(name=ingredient_name).first()
                if ingredient:
                    mix_ingredient = FeedMixIngredient(
                        feed_mix_id=mix.id,
                        ingredient_id=ingredient.id,
                        percentage=percentage
                    )
                    db.session.add(mix_ingredient)
                    total_percentage += percentage
                else:
                    print(f"⚠️ تحذير: المكون '{ingredient_name}' غير موجود في قاعدة البيانات")
            
            # حساب القيم الغذائية
            mix.calculate_nutritional_values()
            
            print(f"تم إضافة: {mix_data['name']} (مجموع النسب: {total_percentage}%)")
            added_count += 1
        
        # حفظ التغييرات
        try:
            db.session.commit()
            print(f"\n✅ تم إضافة {added_count} خلطة علف بنجاح!")
            print(f"إجمالي الخلطات في قاعدة البيانات: {FeedMix.query.count()}")
            
            # عرض تفاصيل الخلطات المضافة
            print("\n📊 تفاصيل الخلطات المضافة:")
            for mix in FeedMix.query.all():
                if mix.calculated_protein > 0:
                    print(f"\n🔸 {mix.name}:")
                    print(f"   البروتين: {mix.calculated_protein}% (هدف: {mix.target_protein}%)")
                    print(f"   الطاقة: {mix.calculated_energy} (هدف: {mix.target_energy})")
                    print(f"   الألياف: {mix.calculated_fiber}% (هدف: {mix.target_fiber}%)")
                    print(f"   التكلفة: {mix.total_cost_per_kg} ريال/كيلو")
                    
        except Exception as e:
            db.session.rollback()
            print(f"❌ خطأ في حفظ البيانات: {e}")

if __name__ == '__main__':
    add_sample_feed_mixes()
