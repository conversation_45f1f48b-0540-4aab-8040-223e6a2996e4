{% extends "base.html" %}

{% block title %}إضافة مكون علف - نظام إدارة مزرعة الألبان{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-plus"></i> إضافة مكون علف جديد
            </h1>
            <a href="{{ url_for('feed_ingredients_list') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> العودة للقائمة
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-seedling"></i> بيانات مكون العلف
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.name.label(class="form-label") }}
                        {{ form.name(class="form-control") }}
                        {% if form.name.errors %}
                            <div class="text-danger">
                                {% for error in form.name.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.protein_percentage.label(class="form-label") }}
                                <div class="input-group">
                                    {{ form.protein_percentage(class="form-control") }}
                                    <span class="input-group-text">%</span>
                                </div>
                                {% if form.protein_percentage.errors %}
                                    <div class="text-danger">
                                        {% for error in form.protein_percentage.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.energy_mcal_kg.label(class="form-label") }}
                                <div class="input-group">
                                    {{ form.energy_mcal_kg(class="form-control") }}
                                    <span class="input-group-text">ميجا كالوري/كيلو</span>
                                </div>
                                {% if form.energy_mcal_kg.errors %}
                                    <div class="text-danger">
                                        {% for error in form.energy_mcal_kg.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.fiber_percentage.label(class="form-label") }}
                                <div class="input-group">
                                    {{ form.fiber_percentage(class="form-control") }}
                                    <span class="input-group-text">%</span>
                                </div>
                                {% if form.fiber_percentage.errors %}
                                    <div class="text-danger">
                                        {% for error in form.fiber_percentage.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.moisture_percentage.label(class="form-label") }}
                                <div class="input-group">
                                    {{ form.moisture_percentage(class="form-control") }}
                                    <span class="input-group-text">%</span>
                                </div>
                                {% if form.moisture_percentage.errors %}
                                    <div class="text-danger">
                                        {% for error in form.moisture_percentage.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.price_per_kg.label(class="form-label") }}
                                <div class="input-group">
                                    {{ form.price_per_kg(class="form-control") }}
                                    <span class="input-group-text">ريال</span>
                                </div>
                                {% if form.price_per_kg.errors %}
                                    <div class="text-danger">
                                        {% for error in form.price_per_kg.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.supplier.label(class="form-label") }}
                                {{ form.supplier(class="form-control") }}
                                {% if form.supplier.errors %}
                                    <div class="text-danger">
                                        {% for error in form.supplier.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.notes.label(class="form-label") }}
                        {{ form.notes(class="form-control", rows="3") }}
                        {% if form.notes.errors %}
                            <div class="text-danger">
                                {% for error in form.notes.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('feed_ingredients_list') }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times"></i> إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ المكون
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
