{% extends "base.html" %}

{% block title %}إضافة مكون علف - نظام إدارة مزرعة الألبان{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-plus"></i> إضافة مكون علف جديد
            </h1>
            <div>
                <a href="{{ url_for('add_custom_feed_ingredient') }}" class="btn btn-outline-primary">
                    <i class="fas fa-edit"></i> إضافة مكون مخصص
                </a>
                <a href="{{ url_for('feed_ingredients_list') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> العودة للقائمة
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-seedling"></i> اختيار مكون العلف
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>اختر من القائمة:</strong> اختر مكون علف من القائمة المحددة مسبقاً وستظهر القيم الغذائية تلقائياً.
                    يمكنك تعديل السعر والمورد حسب السوق المحلي.
                </div>

                <form method="POST" id="ingredientForm">
                    {{ form.hidden_tag() }}

                    <div class="mb-3">
                        {{ form.predefined_ingredient.label(class="form-label") }}
                        {{ form.predefined_ingredient(class="form-select", id="ingredientSelect") }}
                        {% if form.predefined_ingredient.errors %}
                            <div class="text-danger">
                                {% for error in form.predefined_ingredient.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- معاينة القيم الغذائية -->
                    <div id="nutritionalPreview" class="card bg-light mb-3" style="display: none;">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-chart-bar"></i> القيم الغذائية للمكون المختار
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <label class="form-label text-muted">البروتين</label>
                                    <div class="h6 text-primary" id="previewProtein">-</div>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label text-muted">الطاقة</label>
                                    <div class="h6 text-success" id="previewEnergy">-</div>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label text-muted">الألياف</label>
                                    <div class="h6 text-info" id="previewFiber">-</div>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label text-muted">الرطوبة</label>
                                    <div class="h6 text-warning" id="previewMoisture">-</div>
                                </div>
                            </div>
                            <div class="mt-2">
                                <label class="form-label text-muted">الوصف</label>
                                <div class="small text-muted" id="previewNotes">-</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.price_per_kg.label(class="form-label") }}
                                <div class="input-group">
                                    {{ form.price_per_kg(class="form-control", placeholder="أدخل السعر الحالي في السوق") }}
                                    <span class="input-group-text">ريال</span>
                                </div>
                                {% if form.price_per_kg.errors %}
                                    <div class="text-danger">
                                        {% for error in form.price_per_kg.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <small class="text-muted">يمكنك تعديل السعر حسب السوق المحلي</small>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.supplier.label(class="form-label") }}
                                {{ form.supplier(class="form-control", placeholder="اختياري - اسم المورد المحلي") }}
                                {% if form.supplier.errors %}
                                    <div class="text-danger">
                                        {% for error in form.supplier.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <small class="text-muted">اتركه فارغاً لاستخدام المورد الافتراضي</small>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        {{ form.notes.label(class="form-label") }}
                        {{ form.notes(class="form-control", rows="3", placeholder="ملاحظات إضافية حول هذا المكون...") }}
                        {% if form.notes.errors %}
                            <div class="text-danger">
                                {% for error in form.notes.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                        <small class="text-muted">ستُضاف إلى الوصف الأساسي للمكون</small>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('feed_ingredients_list') }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times"></i> إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ المكون
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const ingredientSelect = document.getElementById('ingredientSelect');
    const nutritionalPreview = document.getElementById('nutritionalPreview');
    const priceInput = document.querySelector('input[name="price_per_kg"]');
    const supplierInput = document.querySelector('input[name="supplier"]');

    ingredientSelect.addEventListener('change', function() {
        const selectedIngredient = this.value;

        if (selectedIngredient) {
            // إظهار منطقة المعاينة
            nutritionalPreview.style.display = 'block';

            // جلب بيانات المكون من API
            fetch(`/api/ingredient-data/${encodeURIComponent(selectedIngredient)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        console.error('خطأ:', data.error);
                        return;
                    }

                    // تحديث المعاينة
                    document.getElementById('previewProtein').textContent = data.protein_percentage + '%';
                    document.getElementById('previewEnergy').textContent = data.energy_mcal_kg + ' ميجا كالوري/كيلو';
                    document.getElementById('previewFiber').textContent = data.fiber_percentage + '%';
                    document.getElementById('previewMoisture').textContent = data.moisture_percentage + '%';
                    document.getElementById('previewNotes').textContent = data.notes || 'لا توجد ملاحظات';

                    // تحديث السعر والمورد الافتراضي
                    if (priceInput && !priceInput.value) {
                        priceInput.value = data.price_per_kg;
                    }
                    if (supplierInput && !supplierInput.value) {
                        supplierInput.value = data.supplier || '';
                    }
                })
                .catch(error => {
                    console.error('خطأ في جلب البيانات:', error);
                    nutritionalPreview.style.display = 'none';
                });
        } else {
            // إخفاء المعاينة إذا لم يتم اختيار مكون
            nutritionalPreview.style.display = 'none';
        }
    });

    // تحديث المعاينة إذا كان هناك مكون مختار مسبقاً
    if (ingredientSelect.value) {
        ingredientSelect.dispatchEvent(new Event('change'));
    }
});
</script>
{% endblock %}
