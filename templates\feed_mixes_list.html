{% extends "base.html" %}

{% block title %}خلطات العلف - نظام إدارة مزرعة الألبان{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-blender"></i> خلطات العلف
            </h1>
            <a href="{{ url_for('add_feed_mix') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> إضافة خلطة جديدة
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                {% if mixes %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>اسم الخلطة</th>
                                    <th>الوصف</th>
                                    <th>البروتين المستهدف</th>
                                    <th>البروتين الفعلي</th>
                                    <th>الطاقة المستهدفة</th>
                                    <th>الطاقة الفعلية</th>
                                    <th>التكلفة/كيلو</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for mix in mixes %}
                                <tr>
                                    <td><strong>{{ mix.name }}</strong></td>
                                    <td>{{ mix.description[:50] + '...' if mix.description and mix.description|length > 50 else mix.description or '-' }}</td>
                                    <td>{{ mix.target_protein }}%</td>
                                    <td>
                                        {% if mix.calculated_protein > 0 %}
                                            <span class="badge bg-{{ 'success' if abs(mix.calculated_protein - mix.target_protein) <= 1 else 'warning' }}">
                                                {{ mix.calculated_protein }}%
                                            </span>
                                        {% else %}
                                            <span class="text-muted">غير محسوب</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ mix.target_energy }}</td>
                                    <td>
                                        {% if mix.calculated_energy > 0 %}
                                            <span class="badge bg-{{ 'success' if abs(mix.calculated_energy - mix.target_energy) <= 0.2 else 'warning' }}">
                                                {{ mix.calculated_energy }}
                                            </span>
                                        {% else %}
                                            <span class="text-muted">غير محسوب</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if mix.total_cost_per_kg > 0 %}
                                            {{ mix.total_cost_per_kg }} ريال
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="{{ url_for('feed_mix_details', mix_id=mix.id) }}" class="btn btn-outline-primary" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ url_for('configure_feed_mix', mix_id=mix.id) }}" class="btn btn-outline-info" title="تكوين الخلطة">
                                                <i class="fas fa-cogs"></i>
                                            </a>
                                            <button type="button" class="btn btn-outline-warning" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-danger" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-blender fa-3x text-muted mb-3"></i>
                        <h4 class="text-muted">لا توجد خلطات علف مسجلة</h4>
                        <p class="text-muted">ابدأ بإضافة أول خلطة علف</p>
                        <a href="{{ url_for('add_feed_mix') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> إضافة خلطة جديدة
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
